package com.adoreboard.emotics.api.revenueAtRiskTemplate.service;

import com.adoreboard.emotics.common.model.revenueAtRisk.RevenueAtRiskTemplate;

import java.util.List;

public interface RevenueAtRiskTemplateService {

    // Workspace template operations
    List<RevenueAtRiskTemplate> getWorkspaceTemplates(Integer workspaceId);

    RevenueAtRiskTemplate getTemplateById(Integer templateId);

    RevenueAtRiskTemplate createWorkspaceTemplate(RevenueAtRiskTemplate template);

    RevenueAtRiskTemplate updateTemplate(RevenueAtRiskTemplate template);

    void deleteTemplate(Integer templateId);

    // Default template operations
    RevenueAtRiskTemplate getDefaultTemplate(Integer workspaceId);

    void setDefaultTemplate(Integer templateId, Integer workspaceId);

    // Validation
    boolean isTemplateNameValid(Integer workspaceId, String name, Integer excludeTemplateId);
}
