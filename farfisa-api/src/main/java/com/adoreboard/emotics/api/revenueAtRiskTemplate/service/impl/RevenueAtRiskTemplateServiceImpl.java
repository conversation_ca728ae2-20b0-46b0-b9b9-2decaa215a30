package com.adoreboard.emotics.api.revenueAtRiskTemplate.service.impl;

import com.adoreboard.emotics.api.revenueAtRiskTemplate.service.RevenueAtRiskTemplateService;
import com.adoreboard.emotics.common.mapper.RevenueAtRiskTemplateMapper;
import com.adoreboard.emotics.common.model.revenueAtRisk.RevenueAtRiskTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

@Service
@Transactional
public class RevenueAtRiskTemplateServiceImpl implements RevenueAtRiskTemplateService {

    @Autowired
    private RevenueAtRiskTemplateMapper revenueAtRiskTemplateMapper;

    @Override
    public List<RevenueAtRiskTemplate> getWorkspaceTemplates(Integer workspaceId) {
        return revenueAtRiskTemplateMapper.getWorkspaceTemplates(workspaceId);
    }

    @Override
    public RevenueAtRiskTemplate getTemplateById(Integer templateId) {
        return revenueAtRiskTemplateMapper.getTemplateById(templateId);
    }

    @Override
    public RevenueAtRiskTemplate createWorkspaceTemplate(RevenueAtRiskTemplate template) {
        // Validate template name uniqueness
        if (!isTemplateNameValid(template.getWorkspaceId(), template.getName(), null)) {
            throw new IllegalArgumentException("Template name already exists in this workspace");
        }

        // If this is marked as default, unset any existing default
        if (template.isDefaultTemplate()) {
            revenueAtRiskTemplateMapper.unsetDefaultTemplate(template.getWorkspaceId());
        }

        revenueAtRiskTemplateMapper.createWorkspaceTemplate(template);
        return template;
    }

    @Override
    public RevenueAtRiskTemplate updateTemplate(RevenueAtRiskTemplate template) {
        // Validate template name uniqueness (excluding current template)
        if (!isTemplateNameValid(template.getWorkspaceId(), template.getName(), template.getId())) {
            throw new IllegalArgumentException("Template name already exists in this workspace");
        }

        // If this is marked as default, unset any existing default
        if (template.isDefaultTemplate()) {
            revenueAtRiskTemplateMapper.unsetDefaultTemplate(template.getWorkspaceId());
        }

        revenueAtRiskTemplateMapper.updateTemplate(template);
        return template;
    }

    @Override
    public void deleteTemplate(Integer templateId) {
        // Use soft delete to maintain referential integrity
        revenueAtRiskTemplateMapper.softDeleteTemplate(templateId);
    }

    @Override
    public RevenueAtRiskTemplate getDefaultTemplate(Integer workspaceId) {
        return revenueAtRiskTemplateMapper.getDefaultTemplate(workspaceId);
    }

    @Override
    public void setDefaultTemplate(Integer templateId, Integer workspaceId) {
        // First unset any existing default
        revenueAtRiskTemplateMapper.unsetDefaultTemplate(workspaceId);

        // Then set the new default
        revenueAtRiskTemplateMapper.setDefaultTemplate(templateId);
    }

    @Override
    public boolean isTemplateNameValid(Integer workspaceId, String name, Integer excludeTemplateId) {
        boolean exists = revenueAtRiskTemplateMapper.existsTemplateByName(workspaceId, name);

        // If we're updating an existing template, check if the existing name belongs to the same template
        if (exists && excludeTemplateId != null) {
            RevenueAtRiskTemplate existingTemplate = revenueAtRiskTemplateMapper.getTemplateById(excludeTemplateId);
            return existingTemplate != null && existingTemplate.getName().equals(name);
        }

        return !exists;
    }
}
