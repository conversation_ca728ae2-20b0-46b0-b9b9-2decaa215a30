package com.adoreboard.emotics.api.dataset.controller;

import com.adoreboard.emotics.api.revenueAtRiskTemplate.service.DatasetRevenueAtRiskService;
import com.adoreboard.emotics.api.revenueAtRiskTemplate.service.RevenueAtRiskTemplateService;
import com.adoreboard.emotics.common.model.revenueAtRisk.DatasetRevenueAtRisk;
import com.adoreboard.emotics.common.model.revenueAtRisk.RevenueAtRiskTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping(value = "/api/datasets")
public class DatasetRevenueAtRiskController {

    @Autowired
    private RevenueAtRiskTemplateService revenueAtRiskTemplateService;

    @Autowired
    private DatasetRevenueAtRiskService datasetRevenueAtRiskService;

    // ============================================================================
    // Dataset Template Endpoints
    // ============================================================================

    /**
     * Get all Revenue At Risk templates for a dataset
     */
    @GetMapping("/{datasetId}/revenue-at-risks")
    public ResponseEntity<List<DatasetRevenueAtRisk>> getDatasetTemplates(
            @PathVariable Integer datasetId) {
        List<DatasetRevenueAtRisk> datasetRevenueAtRisks = datasetRevenueAtRiskService.getDatasetTemplates(datasetId);
        return ResponseEntity.ok(datasetRevenueAtRisks);
    }

    /**
     * Create a new dataset Revenue At Risk template
     */
    @PostMapping("/{datasetId}/revenue-at-risks")
    public ResponseEntity<DatasetRevenueAtRisk> createDatasetTemplate(
            @PathVariable Integer datasetId,
            @RequestBody DatasetRevenueAtRisk datasetRevenueAtRisk) {

        datasetRevenueAtRisk.setDatasetId(datasetId);
        DatasetRevenueAtRisk revenueAtRisk = datasetRevenueAtRiskService.createDatasetTemplate(datasetRevenueAtRisk);
        return ResponseEntity.ok(revenueAtRisk);
    }

    /**
     * Update a dataset Revenue At Risk template
     */
    @PutMapping("/{datasetId}/revenue-at-risks/{rarId}")
    public ResponseEntity<DatasetRevenueAtRisk> updateDatasetTemplate(
            @PathVariable Integer datasetId,
            @PathVariable Integer rarId,
            @RequestBody DatasetRevenueAtRisk datasetTemplate) {

        datasetTemplate.setId(rarId);
        datasetTemplate.setDatasetId(datasetId);
        DatasetRevenueAtRisk updatedTemplate = datasetRevenueAtRiskService.updateDatasetTemplate(datasetTemplate);
        return ResponseEntity.ok(updatedTemplate);
    }

    /**
     * Set a dataset template as active
     */
    @PostMapping("/{datasetId}/revenue-at-risks/{rarId}/activate")
    public ResponseEntity<Void> setActiveTemplate(
            @PathVariable Integer datasetId,
            @PathVariable Integer rarId) {

        datasetRevenueAtRiskService.setActiveTemplate(datasetId, rarId);
        return ResponseEntity.ok().build();
    }

    /**
     * Delete a dataset Revenue At Risk template
     */
    @DeleteMapping("/{datasetId}/revenue-at-risks/{rarId}")
    public ResponseEntity<Void> deleteDatasetTemplate(
            @PathVariable Integer datasetId,
            @PathVariable Integer rarId) {

        datasetRevenueAtRiskService.deleteDatasetTemplate(rarId);
        return ResponseEntity.ok().build();
    }
}
