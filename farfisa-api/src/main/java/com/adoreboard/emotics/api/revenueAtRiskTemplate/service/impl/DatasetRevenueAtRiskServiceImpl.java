package com.adoreboard.emotics.api.revenueAtRiskTemplate.service.impl;

import com.adoreboard.emotics.api.revenueAtRiskTemplate.service.DatasetRevenueAtRiskService;
import com.adoreboard.emotics.api.revenueAtRiskTemplate.service.RevenueAtRiskTemplateService;
import com.adoreboard.emotics.api.valueAtRisk.service.ValueAtRiskService;
import com.adoreboard.emotics.common.mapper.DatasetMapper;
import com.adoreboard.emotics.common.mapper.DatasetRevenueAtRiskMapper;
import com.adoreboard.emotics.common.model.Dataset;
import com.adoreboard.emotics.common.model.revenueAtRisk.DatasetRevenueAtRisk;
import com.adoreboard.emotics.common.model.revenueAtRisk.RevenueAtRiskTemplate;
import com.adoreboard.emotics.common.model.revenueAtRisk.ValueAtRiskInfo;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.List;

@Service
@RequiredArgsConstructor
public class DatasetRevenueAtRiskServiceImpl implements DatasetRevenueAtRiskService {
    private final DatasetRevenueAtRiskMapper datasetRevenueAtRiskMapper;
    private final RevenueAtRiskTemplateService revenueAtRiskTemplateService;
    private final ValueAtRiskService valueAtRiskService;
    private final DatasetMapper datasetMapper;

    @Override
    public List<DatasetRevenueAtRisk> getDatasetTemplates(Integer datasetId) {
        return datasetRevenueAtRiskMapper.getDatasetTemplates(datasetId);
    }

    @Override
    public DatasetRevenueAtRisk getDatasetTemplateById(Integer id) {
        return datasetRevenueAtRiskMapper.getDatasetTemplateById(id);
    }

    @Override
    public DatasetRevenueAtRisk createDatasetTemplate(DatasetRevenueAtRisk datasetTemplate) {
        datasetTemplate.setCreatedAt(LocalDateTime.now());

        // Calculate Revenue At Risk for the new template
        if (datasetTemplate.getRevenueAtRisk() != null) {
            ValueAtRiskInfo calculatedRar = valueAtRiskService.recalculateVarInfo(
                datasetTemplate.getDatasetId(), datasetTemplate.getRevenueAtRisk());
            datasetTemplate.setRevenueAtRisk(calculatedRar);
        }

        datasetRevenueAtRiskMapper.createDatasetTemplate(datasetTemplate);
        return datasetTemplate;
    }

    @Override
    public DatasetRevenueAtRisk updateDatasetTemplate(DatasetRevenueAtRisk datasetTemplate) {

        // Recalculate Revenue At Risk for the updated template
        if (datasetTemplate.getRevenueAtRisk() != null) {
            ValueAtRiskInfo calculatedRar = valueAtRiskService.recalculateVarInfo(
                datasetTemplate.getDatasetId(), datasetTemplate.getRevenueAtRisk());
            datasetTemplate.setRevenueAtRisk(calculatedRar);
        }

        datasetRevenueAtRiskMapper.updateDatasetTemplate(datasetTemplate);
        return datasetTemplate;
    }

    @Override
    public void deleteDatasetTemplate(Integer id) {
        datasetRevenueAtRiskMapper.deleteDatasetTemplate(id);
    }

    @Override
    public void cloneWorkspaceTemplatesForDataset(Integer datasetId, Integer workspaceId) {
        // Clone all workspace templates to the dataset
        datasetRevenueAtRiskMapper.cloneWorkspaceTemplatesForDataset(datasetId, workspaceId);

        // Get the default template for this workspace
        RevenueAtRiskTemplate defaultTemplate = revenueAtRiskTemplateService.getDefaultTemplate(workspaceId);

        if (defaultTemplate != null) {
            // Find the cloned template and set it as active
            DatasetRevenueAtRisk clonedTemplate = datasetRevenueAtRiskMapper
                .getDatasetTemplateByTemplateId(datasetId, defaultTemplate.getId());

            if (clonedTemplate != null) {
                // Calculate Revenue At Risk for the cloned template
                Dataset dataset = getDatasetById(datasetId);
                ValueAtRiskInfo calculatedRar = valueAtRiskService.recalculateVarInfo(
                    dataset.getId(), clonedTemplate.getRevenueAtRisk());

                clonedTemplate.setRevenueAtRisk(calculatedRar);
                updateDatasetTemplate(clonedTemplate);

                // Set this as the selected template for the dataset
                setActiveTemplate(datasetId, clonedTemplate.getId());
            }
        }
    }

    @Override
    public Integer getSelectedTemplateIdForDataset(Integer datasetId) {
        Dataset dataset = getDatasetById(datasetId);
        return dataset != null ? dataset.getSelectedRarTemplateId() : null;
    }

    @Override
    public void setActiveTemplate(Integer datasetId, Integer templateId) {
        // Update the dataset's selected_rar_id field
        datasetMapper.updateSelectedRarId(datasetId, templateId);
    }

    // Helper method to get dataset by ID
    private Dataset getDatasetById(Integer datasetId) {
        return datasetMapper.selectById(datasetId);
    }
}
