package com.adoreboard.emotics.api.valueAtRisk.service.impl;

import com.adoreboard.emotics.api.dataset.service.TopicService;
import com.adoreboard.emotics.api.valueAtRisk.service.ValueAtRiskService;
import com.adoreboard.emotics.common.enums.ValueAtRiskType;
import com.adoreboard.emotics.common.enums.ValueAtRiskWeight;
import com.adoreboard.emotics.common.mapper.DatasetMapper;
import com.adoreboard.emotics.common.mapper.UserDocMapper;
import com.adoreboard.emotics.common.mapper.UserTopicMapper;
import com.adoreboard.emotics.common.model.Dataset;
import com.adoreboard.emotics.common.model.revenueAtRisk.ValueAtRiskInfo;
import com.adoreboard.emotics.common.model.revenueAtRisk.ValueAtRiskInfoObject;
import com.adoreboard.emotics.common.model.analysis.AnalysisTopic;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

@Service
public class ValueAtRiskServiceImpl implements ValueAtRiskService {

    private static final Logger logger = LogManager.getLogger(ValueAtRiskServiceImpl.class);

    @Autowired DatasetMapper datasetMapper;
    @Autowired UserTopicMapper userTopicMapper;
    @Autowired UserDocMapper userDocMapper;
    @Autowired TopicService topicService;

    @Override
    public void saveDatasetVar(int datasetId) {
        logger.info("Calculating dataset Value-at-Risk values for dataset {}", datasetId);
        ValueAtRiskInfo valueAtRiskInfo = this.getVarInfo(datasetId);
        valueAtRiskInfo = calculateDatasetVar(datasetId, valueAtRiskInfo);
        datasetMapper.updateVarInfo(datasetId, valueAtRiskInfo);
    }


    public void calculcateTopicVar(int datasetId) {
        // Not currently in use, will implement later
    }

    @Override
    public ValueAtRiskInfo getVarInfo(int datasetId) {
        return Optional.ofNullable(datasetMapper.selectValueAtRiskInfo(datasetId))
                .map(ValueAtRiskInfoObject::getValueAtRiskInfo)
                .orElse(new ValueAtRiskInfo(ValueAtRiskType.CUSTOMER, ValueAtRiskWeight.MEDIUM));
    }

    @Override
    public ValueAtRiskInfo setVarInfo(int datasetId, ValueAtRiskInfo info) {
        logger.info("Updating Value-at-Risk info for dataset {} - {}", datasetId, info);

        if(info.getTotalAmount() <= 0) {
            info.setValueAtRiskAmount(BigDecimal.ZERO);
            return info;
        }

        datasetMapper.updateVarInfo(datasetId, info);

        if (info.getTotalAmount() > 0) {
            saveCommentVar(datasetId);
            saveAllTopicsVar(datasetId);
            saveDatasetVar(datasetId);
        }

        info = this.getVarInfo(datasetId);
        logger.info("Updating successfully Value-at-Risk info for dataset {} - {}", datasetId, info);

        return info;

    }

    @Override
    public ValueAtRiskInfo previewVarInfo(int datasetId, ValueAtRiskInfo info) {
        logger.info("Preview Value-at-Risk info for dataset {} - {}", datasetId, info);

        if(info.getTotalAmount() <= 0) {
            info.setValueAtRiskAmount(BigDecimal.ZERO);
            return info;
        }
        calculateCommentVar(datasetId, info);
        calculateAllTopicsVar(datasetId, info);
        return calculateDatasetVar(datasetId, info);
    }

    @Override
    public ValueAtRiskInfo recalculateVarInfo(int datasetId, ValueAtRiskInfo info) {
        logger.info("Recalculating Value-at-Risk info for dataset {} - {}", datasetId, info);
        ValueAtRiskInfo preview = this.previewVarInfo(datasetId, info);
        return this.setVarInfo(datasetId, preview);
    }

    private ValueAtRiskInfo calculateDatasetVar (int datasetId, ValueAtRiskInfo valueAtRiskInfo) {
        logger.info("Calculating dataset Value-at-Risk values for dataset {}", datasetId);
        //List<Integer> topicIds = topicService.retrieveTopicsByDatasetId(datasetId).stream().map(AnalysisTopic::getId).collect(Collectors.toList());
        BigDecimal amount = datasetMapper.selectValueAtRiskAmount(datasetId, valueAtRiskInfo.getTotalAmount(), valueAtRiskInfo.getScaleToTotalPeople().booleanValue(), valueAtRiskInfo.getNumberOfScalePeople(), valueAtRiskInfo.getTotalYear());
        valueAtRiskInfo.setValueAtRiskAmount(amount);
        //int totalYear = Math.max(valueAtRiskInfo.getTotalYear(),1); // Ensure total year is at least 1
        //Set selected themes to the first 3 topics
        //Get topics by data set, then sort by value at risk preview descending, then get the first 3
        List<Integer> topicIds = topicService.retrieveTopicsByDatasetId(datasetId).stream()
                .sorted((t1, t2) -> {
                    BigDecimal t1Amount = Optional.ofNullable(t1.getValueAtRiskAmountPreview()).orElse(t1.getValueAtRiskAmount());
                    BigDecimal t2Amount = Optional.ofNullable(t2.getValueAtRiskAmountPreview()).orElse(t2.getValueAtRiskAmount());
                    return Objects.compare(t2Amount,t1Amount, BigDecimal::compareTo);
                })
                .map(AnalysisTopic::getId)
                .collect(Collectors.toList());
        valueAtRiskInfo.setSelectedThemeIds(topicIds.subList(0, Math.min(3, topicIds.size())));
        logger.info("Dataset Value-at-Risk values for dataset {} - {} successfully calculated", datasetId, valueAtRiskInfo);
        return valueAtRiskInfo;
    }

    private void calculateCommentVar(int datasetId, ValueAtRiskInfo valueAtRiskInfo) {
        logger.info("Calculating Comment Value-at-Risk values for dataset {}", datasetId);

        double totalAmount = valueAtRiskInfo.getTotalAmount();
        int documentCount = datasetMapper.selectById(datasetId).getDocumentCount();

        userDocMapper.updateValueAtRiskAmount(datasetId, totalAmount, valueAtRiskInfo.getValueAtRiskWeight(), valueAtRiskInfo.getScaleToTotalPeople().booleanValue(), valueAtRiskInfo.getNumberOfScalePeople(), valueAtRiskInfo.getTotalYear(), documentCount);
    }

    private void calculateAllTopicsVar(int datasetId, ValueAtRiskInfo valueAtRiskInfo) {
        logger.info("Calculating All topic Value-at-Risk values for dataset {}", datasetId);

        Dataset dataset = datasetMapper.selectById(datasetId);
        userTopicMapper.updateAllTopicsVarPreview(datasetId, valueAtRiskInfo.getTotalAmount(), valueAtRiskInfo.getScaleToTotalPeople(), valueAtRiskInfo.getNumberOfScalePeople(), valueAtRiskInfo.getTotalYear(), dataset.getDocumentCount());
    }

    private void saveAllTopicsVar(int datasetId) {
        userTopicMapper.updateAllTopicsVar(datasetId);
    }

    private void saveCommentVar(int datasetId) {
        this.calculateCommentVar(datasetId, this.getVarInfo(datasetId));
    }
}
