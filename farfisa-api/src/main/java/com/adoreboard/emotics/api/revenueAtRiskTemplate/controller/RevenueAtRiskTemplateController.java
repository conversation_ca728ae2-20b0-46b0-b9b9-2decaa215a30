package com.adoreboard.emotics.api.revenueAtRiskTemplate.controller;

import com.adoreboard.emotics.api.authentication.AuthenticationUser;
import com.adoreboard.emotics.api.revenueAtRiskTemplate.service.DatasetRevenueAtRiskService;
import com.adoreboard.emotics.api.revenueAtRiskTemplate.service.RevenueAtRiskTemplateService;
import com.adoreboard.emotics.api.valueAtRisk.service.ValueAtRiskService;
import com.adoreboard.emotics.api.workspace.service.WorkspaceValidatorService;
import com.adoreboard.emotics.common.model.User;
import com.adoreboard.emotics.common.model.revenueAtRisk.DatasetRevenueAtRisk;
import com.adoreboard.emotics.common.model.revenueAtRisk.RevenueAtRiskTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/api/workspaces")
public class RevenueAtRiskTemplateController {

    @Autowired
    private RevenueAtRiskTemplateService revenueAtRiskTemplateService;
    @Autowired
    private WorkspaceValidatorService workspaceValidatorService;

    @Autowired
    private DatasetRevenueAtRiskService datasetRevenueAtRiskService;

    @Autowired
    private ValueAtRiskService valueAtRiskService;

    // ============================================================================
    // Workspace Template Endpoints
    // ============================================================================

    /**
     * Get all Revenue At Risk templates for a workspace
     */
    @GetMapping("/{workspaceId}/revenue-at-risk-templates")
    public ResponseEntity<List<RevenueAtRiskTemplate>> getWorkspaceTemplates(
            @AuthenticationUser User user,
            @PathVariable Integer workspaceId) {
        List<RevenueAtRiskTemplate> templates = revenueAtRiskTemplateService.getWorkspaceTemplates(workspaceId);
        return ResponseEntity.ok(templates);
    }

    /**
     * Create a new workspace Revenue At Risk template
     */
    @PostMapping("/{workspaceId}/revenue-at-risk-templates")
    public ResponseEntity<RevenueAtRiskTemplate> createWorkspaceTemplate(
            @PathVariable Integer workspaceId,
            @RequestBody RevenueAtRiskTemplate template) {

        template.setWorkspaceId(workspaceId);
        RevenueAtRiskTemplate createdTemplate = revenueAtRiskTemplateService.createWorkspaceTemplate(template);
        return ResponseEntity.ok(createdTemplate);
    }

    /**
     * Update a Revenue At Risk template
     */
    @PutMapping("/{workspaceId}/revenue-at-risk-templates/{templateId}")
    public ResponseEntity<RevenueAtRiskTemplate> updateTemplate(
            @PathVariable Integer templateId,
            @RequestBody RevenueAtRiskTemplate template) {

        template.setId(templateId);
        RevenueAtRiskTemplate updatedTemplate = revenueAtRiskTemplateService.updateTemplate(template);
        return ResponseEntity.ok(updatedTemplate);
    }

    /**
     * Delete a Revenue At Risk template
     */
    @DeleteMapping("/{workspaceId}/revenue-at-risk-templates/{templateId}")
    public ResponseEntity<Void> deleteTemplate(@PathVariable Integer templateId, @PathVariable String workspaceId) {
        revenueAtRiskTemplateService.deleteTemplate(templateId);
        return ResponseEntity.ok().build();
    }

    /**
     * Set a template as default for the workspace
     */
    @PostMapping("/{workspaceId}/revenue-at-risk-templates/{templateId}/set-default")
    public ResponseEntity<Void> setDefaultTemplate(
            @PathVariable Integer workspaceId,
            @PathVariable Integer templateId) {

        revenueAtRiskTemplateService.setDefaultTemplate(templateId, workspaceId);
        return ResponseEntity.ok().build();
    }
}
