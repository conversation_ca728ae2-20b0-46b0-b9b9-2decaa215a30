package com.adoreboard.emotics.api.revenueAtRiskTemplate.service;

import com.adoreboard.emotics.common.model.revenueAtRisk.DatasetRevenueAtRisk;

import java.util.List;

public interface DatasetRevenueAtRiskService {

    // Dataset template operations
    List<DatasetRevenueAtRisk> getDatasetTemplates(Integer datasetId);

    DatasetRevenueAtRisk getDatasetTemplateById(Integer id);

    DatasetRevenueAtRisk createDatasetTemplate(DatasetRevenueAtRisk datasetTemplate);

    DatasetRevenueAtRisk updateDatasetTemplate(DatasetRevenueAtRisk datasetTemplate);

    void deleteDatasetTemplate(Integer id);

    // Dataset creation operations
    void cloneWorkspaceTemplatesForDataset(Integer datasetId, Integer workspaceId);

    Integer getSelectedTemplateIdForDataset(Integer datasetId);

    void setActiveTemplate(Integer datasetId, Integer templateId);
}
