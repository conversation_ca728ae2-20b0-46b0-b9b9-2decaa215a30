--  -----------------------------------------------------
-- Delete existing tables
-- -----------------------------------------------------

DROP VIEW IF EXISTS user_doc_view;
DROP VIEW IF EXISTS bulk_upload_view;
DROP VIEW IF EXISTS bulk_upload_with_progress_map;
DROP VIEW IF EXISTS user_doc_stats;

DROP TABLE IF EXISTS rating_scale;
DROP TABLE IF EXISTS dataset_summary;
DROP TABLE IF EXISTS theme_summary;
DROP TABLE IF EXISTS storyteller_slide;
DROP TABLE IF EXISTS storyteller_report;
DROP TABLE IF EXISTS bulk_upload_dataset_tag;
DROP TABLE IF EXISTS dataset_tag;
DROP TABLE IF EXISTS custom_topic;
DROP TABLE IF EXISTS custom_topic_list;
DROP TABLE IF EXISTS history_saved_actions;
DROP TABLE IF EXISTS insight_illustrative_comments;
DROP TABLE IF EXISTS insight_details;
DROP TABLE IF EXISTS insights_scorecard;
DROP TABLE IF EXISTS regression_model;
DROP TABLE IF EXISTS stop_word;
DROP TABLE IF EXISTS metadata_filters;
DROP TABLE IF EXISTS saved_action_lists;
DROP TABLE IF EXISTS saved_actions;
DROP TABLE IF EXISTS user_topic_doc;
DROP TABLE IF EXISTS user_topic;
DROP TABLE IF EXISTS user_doc;
DROP TABLE IF EXISTS bulk_upload_contents;
DROP TABLE IF EXISTS user_content_metadata;
DROP TABLE IF EXISTS user_content;
DROP TABLE IF EXISTS bulk_upload_status;
DROP TABLE IF EXISTS dataset_custom_charts;
DROP TABLE IF EXISTS dataset_download_details;
DROP TABLE IF EXISTS bulk_upload;
DROP TABLE IF EXISTS registration;
DROP TABLE IF EXISTS usage_record;
DROP TABLE IF EXISTS login_stats;
DROP TABLE IF EXISTS organisation;
DROP TABLE IF EXISTS users;
DROP TABLE IF EXISTS user_tier;
DROP TABLE IF EXISTS wrong_content;
DROP TABLE IF EXISTS translation_data;
DROP TABLE IF EXISTS workspace;
DROP TABLE IF EXISTS workspace_group;
DROP TABLE IF EXISTS dataset_permission;
DROP TABLE IF EXISTS reporting_job_data_source;
DROP TABLE IF EXISTS reporting_job_split_rule;
DROP TABLE IF EXISTS reporting_job_workspace_rule;
DROP TABLE IF EXISTS reporting_job_search_theme;
DROP TABLE IF EXISTS reporting_job_email_config;
DROP TABLE IF EXISTS reporting_job_result;
DROP TABLE IF EXISTS reporting_job_execution_status;
DROP TABLE IF EXISTS reporting_job;

-- -----------------------------------------------------
-- Table `USER_TIER`
-- -----------------------------------------------------

CREATE TABLE IF NOT EXISTS user_tier
(
    id                   SERIAL PRIMARY KEY,
    name                 VARCHAR(30)          DEFAULT NULL,
    cost                 INTEGER     NOT NULL DEFAULT 0,
    max_uploads          INTEGER     NOT NULL DEFAULT 0,
    max_chars            INTEGER     NOT NULL DEFAULT 0,
    duration             INTEGER     NOT NULL DEFAULT 0,
    max_comparable_data  INTEGER     NOT NULL DEFAULT 6,
    product_features     TEXT[],
    max_translated_chars INTEGER     NOT NULL DEFAULT 0,
    stripe_plan_id       VARCHAR(64) NULL
);

-- -----------------------------------------------------
-- Table `USERS`
-- -----------------------------------------------------

CREATE TABLE IF NOT EXISTS users
(
    id                       SERIAL PRIMARY KEY,
    tier_id                  INT REFERENCES user_tier (id),
    login_name               VARCHAR(255) NOT NULL UNIQUE,
    created_on               TIMESTAMP    NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_on               TIMESTAMP    NULL     DEFAULT CURRENT_TIMESTAMP,
    secret                   VARCHAR(64),
    hash                     VARCHAR(64),
    two_factor_enabled       BOOLEAN      NOT NULL DEFAULT FALSE,
    secret_2fa_code          VARCHAR(64),
    first_name               VARCHAR(256),
    last_name                VARCHAR(256),
    email                    VARCHAR(256) NOT NULL UNIQUE,
    role                     INT                   DEFAULT 0,
    login_count              INT                   DEFAULT 0,
    active                   BOOLEAN,
    product_features         TEXT[],
    current_translated_chars INTEGER      NOT NULL DEFAULT 0,
    survey_monkey_token      VARCHAR(200) NULL,
    zendesk_token            VARCHAR(200) NULL,
    contact_number           VARCHAR(20)  NULL,
    sector                   VARCHAR(50)  NULL,
    stripe_customer_id       VARCHAR(64)  NULL,
    company                  VARCHAR(255) NULL,
    zendesk_subdomain        VARCHAR(100)          DEFAULT NULL,
    qualtrics_access_token   VARCHAR(100)          DEFAULT NULL,
    qualtrics_refresh_token  VARCHAR(100)          DEFAULT NULL,
    qualtrics_data_centre    VARCHAR(50)           DEFAULT NULL,
    workspace_ids            INTEGER[]             DEFAULT '{}',
    organisation_id          INTEGER      NULL
);

-- -----------------------------------------------------
-- Table `USAGE_RECORD`
-- -----------------------------------------------------

CREATE TABLE IF NOT EXISTS usage_record
(
    id                        SERIAL PRIMARY KEY,
    tier_id                   INT REFERENCES user_tier (id),
    user_id                   INT REFERENCES users (id),
    max_usage                 INT            DEFAULT 0,
    current_usage             INT            DEFAULT 0,
    max_translation_usage     INT            DEFAULT 0,
    current_translation_usage INT            DEFAULT 0,
    upload_count              INT            DEFAULT 0,
    active                    BOOLEAN        DEFAULT FALSE,
    cycle_start               TIMESTAMP NULL DEFAULT CURRENT_TIMESTAMP,
    cycle_end                 TIMESTAMP NULL
);

-- -----------------------------------------------------
-- Table `organisation`
-- -----------------------------------------------------

CREATE TABLE IF NOT EXISTS organisation
(
    id                SERIAL PRIMARY KEY,
    name              VARCHAR(256),
    owner_id          INTEGER REFERENCES users (id),
    administrator_ids INTEGER[] DEFAULT '{}',
    member_ids        INTEGER[] DEFAULT '{}',
    settings          jsonb NULL
);
-- -----------------------------------------------------
-- Table `workspace`
-- -----------------------------------------------------

CREATE TABLE IF NOT EXISTS workspace
(
    id                SERIAL PRIMARY KEY,
    label             VARCHAR(256),
    owner_id          INTEGER REFERENCES users (id),
    administrator_ids INTEGER[] DEFAULT '{}',
    editor_ids        INTEGER[] DEFAULT '{}',
    viewer_ids        INTEGER[] DEFAULT '{}',
    user_limit        INTEGER   DEFAULT 1,
    organisation_id   INTEGER NULL REFERENCES organisation (id),
    topic_model_type  VARCHAR(4),
    settings          jsonb NULL
);

-- -----------------------------------------------------
-- Table `workspace_group`
-- -----------------------------------------------------

CREATE TABLE IF NOT EXISTS workspace_group
(
    id           SERIAL PRIMARY KEY,
    workspace_id INTEGER REFERENCES workspace (id),
    label        VARCHAR(256),
    description  VARCHAR(256),
    owner_id     INTEGER REFERENCES users (id),
    editor_ids   INTEGER[] DEFAULT '{}',
    viewer_ids   INTEGER[] DEFAULT '{}'
);

-- -----------------------------------------------------
-- Table `REGISTRATION`
-- -----------------------------------------------------

CREATE TABLE IF NOT EXISTS registration
(
    id                  SERIAL PRIMARY KEY,
    user_id             INT REFERENCES users (id) NULL DEFAULT NULL,
    email               VARCHAR(256)              NOT NULL,
    hash                VARCHAR(255)              NOT NULL,
    registration_status VARCHAR(64)               NOT NULL,
    first_name          VARCHAR(256),
    last_name           VARCHAR(256),
    timestamp           TIMESTAMP,
    invite_user_id      INTEGER REFERENCES users (id),
    workspace_id        INTEGER REFERENCES workspace (id),
    role                VARCHAR(64)                    DEFAULT NULL
);

-- -----------------------------------------------------
-- Table `custom_topic_list`
-- -----------------------------------------------------

CREATE TABLE IF NOT EXISTS custom_topic_list
(
    id                     SERIAL UNIQUE,
    user_id                INTEGER REFERENCES users (id) ON DELETE CASCADE,
    custom_topic_list_name VARCHAR(256),
    PRIMARY KEY (id, user_id)
);

-- -----------------------------------------------------
-- Table `custom_topic`
-- -----------------------------------------------------

CREATE TABLE IF NOT EXISTS custom_topic
(
    custom_topic_list_id           INTEGER REFERENCES custom_topic_list (id) ON DELETE CASCADE,
    custom_topic_id                SERIAL UNIQUE,
    custom_topic_label             VARCHAR(512),
    custom_topic_search_components JSONB,
    PRIMARY KEY (custom_topic_list_id, custom_topic_id)
);

-- -----------------------------------------------------
-- Table `DATASET`
-- -----------------------------------------------------

CREATE TABLE IF NOT EXISTS bulk_upload
(
    id                    SERIAL PRIMARY KEY,
    user_id               INTEGER REFERENCES users (id),
    workspace_id          INTEGER REFERENCES workspace (id),
    label                 VARCHAR(256),
    document_count        INTEGER,
    content_characters    INTEGER,
    summary               JSONB,
    pending_changes       BOOLEAN                                                          DEFAULT FALSE,
    status                VARCHAR(64) NULL,
    upload_start          TIMESTAMP                                                        DEFAULT CURRENT_TIMESTAMP,
    num_of_documents      INTEGER,
    execution_id          INTEGER                                                          DEFAULT 0,
    error                 JSONB,
    archived              BOOLEAN                                                          DEFAULT FALSE,
    features              TEXT[],
    metadata_headers      TEXT[]                                                           DEFAULT '{}',
    metadata_columns      INTEGER[]                                                        DEFAULT '{}',
    metadata_types        TEXT[]                                                           DEFAULT '{}',
    custom_topic_list     INTEGER     REFERENCES custom_topic_list (id) ON DELETE SET NULL DEFAULT NULL,
    exclude_auto_topics   BOOLEAN                                                          DEFAULT FALSE,
    saved_action_list_ids INTEGER[]                                                        DEFAULT '{}',
    parent_id             INTEGER                                                                      ,
    value_at_risk_info    JSONB                                                            DEFAULT '{}'::JSONB,
    rating_scale_maps     JSONB                                                            DEFAULT '{}'::JSONB,
    metric_calculation_settings JSONB                                                      DEFAULT '{}'::JSON,
    dataset_domain        VARCHAR(256)  NULL
);

CREATE INDEX index_bulk_upload_user_id
    ON bulk_upload (user_id);
CREATE INDEX index_bulk_upload_workspace_id
    ON bulk_upload (workspace_id);
CREATE INDEX index_bulk_upload_status
    ON bulk_upload (status);

-- -----------------------------------------------------
-- Table `USER_CONTENT`
-- -----------------------------------------------------

CREATE TABLE IF NOT EXISTS user_content
(
    id                SERIAL PRIMARY KEY,
    user_id           INTEGER REFERENCES users (id),
    original_content  TEXT,
    content           TEXT,
    timestamp         TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    content_timestamp TIMESTAMP          DEFAULT NULL,
    metadata          TEXT[],
    language_iso      VARCHAR(16),
    ts_vector_data    tsvector GENERATED ALWAYS AS (to_tsvector('english', replace(content, '''', ''))) STORED
);

CREATE INDEX index_user_content ON user_content USING gin (TO_TSVECTOR('english', content));
CREATE INDEX textSearch_idx ON user_content USING GIN (ts_vector_data);

--------------------------------------------------------------
-- Table `USER_CONTENT_METADATA`
-- -----------------------------------------------------
CREATE TABLE IF NOT EXISTS user_content_metadata (
    id               SERIAL PRIMARY KEY,
    dataset_id   INTEGER NOT NULL REFERENCES bulk_upload (id),
    user_content_id  INTEGER REFERENCES user_content (id),
    metadata        TEXT[],
    row_number       INTEGER,
    timestamp TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP
);

CREATE INDEX idx_user_content_metadata_bulk_upload_id
    ON user_content_metadata (dataset_id);

CREATE INDEX idx_user_content_metadata_user_content_id
    ON user_content_metadata (user_content_id);

-- -----------------------------------------------------
-- Table `dataset_content`
--  join-table of dataset and user_content
-- -----------------------------------------------------

CREATE TABLE IF NOT EXISTS bulk_upload_contents
(
    bulk_upload_id  INTEGER REFERENCES bulk_upload (id) ON DELETE CASCADE,
    user_content_id INTEGER REFERENCES user_content (id) ON DELETE CASCADE,
    PRIMARY KEY (bulk_upload_id, user_content_id)
);

CREATE INDEX index_bulk_upload_contents_buid
    ON bulk_upload_contents (bulk_upload_id);

CREATE INDEX index_bulk_upload_contents_cid
    ON bulk_upload_contents (user_content_id);

-- -----------------------------------------------------
-- Table `USER_DOC`
-- -----------------------------------------------------
--   Un-normalised version of old `USER_DOCUMENT` and
--   `DOCUMENT` tables. If a user submits the same text
--   multiple times there will be duplication but in
--   reality this is rare and much outweights the speed
--   implications.
-- -----------------------------------------------------

CREATE TABLE IF NOT EXISTS user_doc
(
    id               SERIAL PRIMARY KEY,
    user_id          INTEGER REFERENCES users (id),
    bulk_upload_id   INTEGER REFERENCES bulk_upload (id),
    content_id       INTEGER REFERENCES user_content (id),
    analysis_summary JSONB, /* PROBABLY FINE HERE FOR NOW */
    timestamp        TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    bookmarked       BOOLEAN            DEFAULT FALSE,
    parent_doc_id    INTEGER REFERENCES user_doc (id) DEFAULT NULL,
    reweighted_score  numeric
);

CREATE UNIQUE INDEX user_doc_bulk_upload_id_content_id_index ON user_doc (user_id, bulk_upload_id, content_id);

-- -----------------------------------------------------
-- Create index for faster querying --
-- -----------------------------------------------------

CREATE INDEX index_user_doc_user_id
    ON user_doc (user_id);
CREATE INDEX index_user_doc_content_id
    ON user_doc (content_id);
CREATE INDEX index_user_doc_bulk_upload_id
    ON user_doc (bulk_upload_id);
CREATE INDEX index_parent_doc_id
    ON user_doc (parent_doc_id);
-- -----------------------------------------------------
-- View of `USER_DOC`, extract extra fields from the summary
-- -----------------------------------------------------

CREATE OR REPLACE VIEW user_doc_stats AS

SELECT *,
       (analysis_summary ->> 'polarity') :: DOUBLE PRECISION                                                AS polarity,
       ARRAY(SELECT JSONB_ARRAY_ELEMENTS_TEXT(analysis_summary -> 'emotionIndexesAvg') :: DOUBLE PRECISION) AS emotion_indexes,
       ARRAY(SELECT JSONB_ARRAY_ELEMENTS_TEXT(analysis_summary -> 'emotionSums') :: INTEGER)                AS emotion_sums
FROM user_doc;

-- -----------------------------------------------------
-- View of `USER_DOC`, extract extra fields from the summary, and contents joined
-- -----------------------------------------------------

CREATE OR REPLACE VIEW user_doc_view AS (
 SELECT uc.content,
    uc.original_content,
    uc.metadata,
    ud.id,
    ud.user_id,
    ud.bulk_upload_id,
    ud.content_id,
    ud.analysis_summary,
    ud."timestamp",
    ud.parent_doc_id,
    (ud.analysis_summary ->> 'polarity'::text)::double precision                                                                             AS polarity,
    ARRAY(SELECT jsonb_array_elements_text(ud.analysis_summary -> 'emotionIndexesAvg'::text)::double precision AS jsonb_array_elements_text) AS emotion_indexes,
    ARRAY(SELECT jsonb_array_elements_text(ud.analysis_summary -> 'emotionSums'::text)::integer AS jsonb_array_elements_text)                AS emotion_sums,
    uc.content_timestamp,
    ud.bookmarked,
    uc.ts_vector_data,
    ud.reweighted_score
FROM user_doc ud
LEFT JOIN user_content uc ON ud.content_id = uc.id);


-- -----------------------------------------------------
-- WRONG_CONTENT table
-- -----------------------------------------------------

CREATE TABLE IF NOT EXISTS wrong_content
(
    id               SERIAL PRIMARY KEY,
    content          TEXT,
    analysis_summary JSONB,
    corrected_index  VARCHAR(12)
);

-- -----------------------------------------------------
-- TABLE JOURNEY_STAGE
-- -----------------------------------------------------

CREATE TABLE IF NOT EXISTS search_components
(
    id                  SERIAL PRIMARY KEY,
    query               TEXT,
    exact               BOOLEAN,
    distinct_content    BOOLEAN,
    score_filters       JSONB,
    exclude_content_ids INTEGER[]
);

-- -----------------------------------------------------
-- TABLE JOURNEY
-- -----------------------------------------------------

CREATE TABLE IF NOT EXISTS journey
(
    id             SERIAL PRIMARY KEY,
    bulk_upload_id INTEGER REFERENCES bulk_upload (id) ON DELETE CASCADE,
    label          VARCHAR(512),
    status         VARCHAR(20)
);

-- -----------------------------------------------------
-- TABLE JOURNEY_STAGE
-- -----------------------------------------------------

CREATE TABLE IF NOT EXISTS journey_stage
(
    id                  SERIAL PRIMARY KEY,
    journey_id          INTEGER REFERENCES journey (id) ON DELETE CASCADE,
    label               VARCHAR(512),
    position            INTEGER,
    exemplar_topic_id   INTEGER,
    exemplar_snippet_id INTEGER,
    search_id           INTEGER REFERENCES search_components (id) ON DELETE CASCADE,
    summary             JSONB,
    document_count      INTEGER,
    status              VARCHAR(20)
);

-- -----------------------------------------------------
-- Table `USER_TOPIC`
-- -----------------------------------------------------

CREATE TABLE IF NOT EXISTS user_topic
(
    id                           SERIAL PRIMARY KEY,
    bulk_upload_id               INTEGER REFERENCES bulk_upload (id),
    topic_label                  VARCHAR(512),
    original_topic_label         VARCHAR(512),
    emotion_index_id             INTEGER,
    topic_score                  DOUBLE PRECISION,
    tone                         DOUBLE PRECISION,
    polarity                     DOUBLE PRECISION,
    high_intensity_percent       DOUBLE PRECISION,
    med_intensity_percent        DOUBLE PRECISION,
    low_intensity_percent        DOUBLE PRECISION,
    emotion_indexes              DOUBLE PRECISION[],
    num_of_documents             INTEGER,
    num_of_positive_documents    INTEGER,
    num_of_negative_documents    INTEGER,
    num_of_words                 INTEGER,
    num_of_emotions              INTEGER,
    num_of_subtopics             INTEGER,
    parent_id                    INTEGER,
    swot                         JSONB,
    emotion_sums                 INTEGER[],
    emotions_aggregated_by_words DOUBLE PRECISION[],
    status                       VARCHAR(64) NOT NULL DEFAULT 'enabled',
    type                         VARCHAR(64) NOT NULL DEFAULT 'overview',
    bucket_session               INTEGER,
    journey_stage_id             INTEGER REFERENCES journey_stage (id),
    custom_topic                 BOOLEAN              DEFAULT FALSE,
    value_at_risk_amount         NUMERIC,
    value_at_risk_amount_preview NUMERIC
);

CREATE INDEX index_user_topic_bulk_upload_id
    ON user_topic (bulk_upload_id);

CREATE INDEX index_user_topic_parent_id
    ON user_topic (parent_id);

-- -----------------------------------------------------
-- Table `USER_TOPIC_DOC`
-- -----------------------------------------------------

CREATE TABLE IF NOT EXISTS user_topic_doc
(
    user_topic_id INTEGER REFERENCES user_topic (id) ON DELETE CASCADE,
    user_doc_id   INTEGER REFERENCES user_doc (id) ON DELETE CASCADE,
    PRIMARY KEY (user_topic_id, user_doc_id)
);

CREATE INDEX index_user_topic_doc_covering ON user_topic_doc (user_doc_id, user_topic_id);
CREATE INDEX index_user_topic_doc_user_doc_id ON user_topic_doc (user_doc_id);
CREATE INDEX index_user_topic_doc_user_topic_id ON user_topic_doc (user_topic_id);
-- Lukas Eder recommended this covering index

-- -----------------------------------------------------
-- Table `STOP_WORD`
-- -----------------------------------------------------

CREATE TABLE IF NOT EXISTS stop_word
(
    id             SERIAL PRIMARY KEY,
    bulk_upload_id INTEGER REFERENCES bulk_upload (id) ON DELETE CASCADE,
    stop_word      VARCHAR(512),
    status         VARCHAR(32),
    created        TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    workspace_id   INTEGER NULL REFERENCES workspace (id)
);
CREATE INDEX index_stop_word_bulk_upload_id
    ON stop_word (bulk_upload_id);

-- -----------------------------------------------------
-- Table `DATASET_PROGRESS_STATUS`
-- -----------------------------------------------------

CREATE TABLE IF NOT EXISTS bulk_upload_status
(
    bulk_upload_id INTEGER REFERENCES bulk_upload (id) ON DELETE CASCADE,
    status         VARCHAR(64)                         NOT NULL,
    started_at     TIMESTAMP DEFAULT CURRENT_TIMESTAMP NOT NULL,
    updated_at     TIMESTAMP DEFAULT CURRENT_TIMESTAMP NOT NULL,
    num_of_items   BIGINT    DEFAULT 1,
    current_item   BIGINT    DEFAULT 0,
    PRIMARY KEY (bulk_upload_id, status)
);

-- -----------------------------------------------------
-- Table `TRANSLATION_DATA`
-- -----------------------------------------------------

CREATE TABLE IF NOT EXISTS translation_data
(
    id              SERIAL PRIMARY KEY,
    timestamp       TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    original_data   TEXT,
    translated_data TEXT,
    hash_code       VARCHAR(64) UNIQUE
);

CREATE INDEX translation_hash ON translation_data (hash_code);

-- -----------------------------------------------------
-- Table `LOGIN_STATS`
-- -----------------------------------------------------

CREATE TABLE IF NOT EXISTS login_stats
(
    id                SERIAL PRIMARY KEY,
    user_id           INT REFERENCES users (id),
    request_type      VARCHAR(64)  NULL,
    request_timestamp TIMESTAMP    NULL DEFAULT CURRENT_TIMESTAMP,
    ip_address        VARCHAR(64)  NULL,
    browser           VARCHAR(200) NULL
);

CREATE INDEX index_login_stats
    ON login_stats (user_id);

-- -----------------------------------------------------
-- Table `DATASET_CUSTOM_CHARTS`
-- -----------------------------------------------------

CREATE TABLE IF NOT EXISTS dataset_custom_charts
(
    dataset_id         INTEGER REFERENCES bulk_upload (id),
    custom_chart_type  VARCHAR(50) NOT NULL,
    custom_chart_title VARCHAR(256),
    custom_chart_description VARCHAR(256),
    custom_topic_list  JSONB       NOT NULL DEFAULT '[]'::JSONB,

    CONSTRAINT dataset_custom_charts_pk
        PRIMARY KEY (dataset_id, custom_chart_type)
);

-- -----------------------------------------------------
-- Table `DATASET_DOWNLOAD_DETAILS`
-- -----------------------------------------------------

CREATE TABLE IF NOT EXISTS dataset_download_details
(
    dataset_id                 INTEGER REFERENCES bulk_upload (id) PRIMARY KEY,
    analysis_download_location VARCHAR(356),
    original_download_location VARCHAR(356)
);

-- -----------------------------------------------------
-- Table `INSIGHT_DETAILS`
-- -----------------------------------------------------

CREATE TABLE IF NOT EXISTS insight_details
(
    dataset_id              INTEGER REFERENCES bulk_upload (id),
    insights_type           VARCHAR(50),
    scorecard_title         TEXT,
    key_areas_title         TEXT,
    key_areas_subtitle      TEXT,
    key_area_one_headline   TEXT,
    key_area_two_headline   TEXT,
    key_area_three_headline TEXT,
    key_area_one_topic_id   INTEGER REFERENCES user_topic (id),
    key_area_two_topic_id   INTEGER REFERENCES user_topic (id),
    key_area_three_topic_id INTEGER REFERENCES user_topic (id),
    correlations            TEXT[] DEFAULT NULL,

    CONSTRAINT insights_details_pk
        PRIMARY KEY (dataset_id, insights_type)
);

-- -----------------------------------------------------
-- Table `INSIGHT_SCORECARD`
-- -----------------------------------------------------

CREATE TABLE IF NOT EXISTS insights_scorecard
(
    dataset_id           INTEGER REFERENCES bulk_upload (id) ON DELETE CASCADE,
    scorecard_id         SERIAL UNIQUE PRIMARY KEY,
    insights             JSONB,
    default_scorecard    BOOLEAN        DEFAULT FALSE,
    insights_action_plan JSONB NOT NULL DEFAULT '{}'::JSONB,
    created_at           TIMESTAMP DEFAULT now()
);

-- -----------------------------------------------------
-- Table `INSIGHT_ILLUSTRATIVE_COMMENTS`
-- -----------------------------------------------------

CREATE TABLE IF NOT EXISTS insight_illustrative_comments
(
    dataset_id    INTEGER REFERENCES bulk_upload (id) ON DELETE CASCADE,
    insights_type VARCHAR(50),
    topic_id      INTEGER REFERENCES user_topic (id) ON DELETE CASCADE,
    comment_id    INTEGER, -- Reference managed by trigger

    CONSTRAINT insights_illustrative_comments_pk
        PRIMARY KEY (dataset_id, insights_type, topic_id)
);

-- -----------------------------------------------------
-- Table `SAVED_ACTIONS`
-- -----------------------------------------------------

CREATE TABLE IF NOT EXISTS saved_actions
(
    id           SERIAL PRIMARY KEY,
    user_id      INTEGER REFERENCES users (id) ON DELETE CASCADE,
    type         VARCHAR(32),
    components   JSONB,
    workspace_id INTEGER REFERENCES workspace (id) ON DELETE CASCADE
);

-- -----------------------------------------------------
-- Table `SAVED_ACTIONS`
-- -----------------------------------------------------

CREATE TABLE IF NOT EXISTS saved_action_lists
(
    id                      SERIAL PRIMARY KEY,
    user_id                 INTEGER REFERENCES users (id) ON DELETE CASCADE,
    label                   VARCHAR(128),
    list                    INTEGER[],
    workspace_id            INTEGER REFERENCES workspace (id) ON DELETE CASCADE,
    is_default              BOOLEAN DEFAULT FALSE,
    target_workspace_ids    INTEGER[] DEFAULT NULL
);

-- -----------------------------------------------------
-- Table `REgression_model`
-- -----------------------------------------------------
CREATE TABLE IF NOT EXISTS regression_model
(
    id             SERIAL PRIMARY KEY,
    dataset_id     INTEGER REFERENCES bulk_upload (id),
    metadata_index INTEGER,
    model          JSONB,
    type           VARCHAR(128)
);

-- -----------------------------------------------------
-- Table `metadata_filters`
-- -----------------------------------------------------

CREATE TABLE IF NOT EXISTS metadata_filters
(
    filter_list_id SERIAL PRIMARY KEY,
    filters        TEXT,
    topic_id       INTEGER REFERENCES user_topic (id) DEFAULT NULL,
    unassigned     BOOLEAN                            DEFAULT FALSE,
    chart_emotion  INTEGER                            DEFAULT NULL,
    filter_list_parent_id   INTEGER REFERENCES metadata_filters (filter_list_id) ON DELETE SET NULL DEFAULT NULL,
    label                   VARCHAR(256) DEFAULT NULL,
    list_type               VARCHAR(64),
    parent_id               INTEGER REFERENCES bulk_upload (id) ON DELETE CASCADE DEFAULT NULL,
    child_id                INTEGER REFERENCES bulk_upload (id) ON DELETE SET NULL DEFAULT NULL,
    split_by                JSONB DEFAULT '{}'::JSONB
);

-- -----------------------------------------------------
-- Table `dataset_tag`
-- -----------------------------------------------------

CREATE TABLE IF NOT EXISTS dataset_tag
(
    id           SERIAL PRIMARY KEY,
    user_id      INTEGER REFERENCES users (id) ON DELETE CASCADE,
    workspace_id INTEGER REFERENCES workspace (id),
    tag_name     VARCHAR(128),
    description  TEXT
);

-- -----------------------------------------------------
-- Table `dataset_permission`
-- -----------------------------------------------------

CREATE TABLE IF NOT EXISTS dataset_permission
(
    dataset_id      INTEGER REFERENCES bulk_upload (id),
    workspace_id    INTEGER REFERENCES workspace (id),
    permission_type VARCHAR(16) NOT NULL,
    editor_ids      INTEGER[] DEFAULT '{}',
    viewer_ids      INTEGER[] DEFAULT '{}',
    group_id        INTEGER,
    CONSTRAINT dataset_permission_fk PRIMARY KEY (dataset_id, workspace_id)
);

-- -----------------------------------------------------
-- Table `bulk_upload_dataset_tag`
-- -----------------------------------------------------

CREATE TABLE IF NOT EXISTS bulk_upload_dataset_tag
(
    dataset_id INTEGER REFERENCES bulk_upload (id) ON DELETE CASCADE,
    tag_id     INTEGER REFERENCES dataset_tag (id) ON DELETE CASCADE,

    CONSTRAINT bulk_upload_dataset_tag_pk
        PRIMARY KEY (dataset_id, tag_id)
);

CREATE TABLE IF NOT EXISTS history_saved_actions
(
    id              SERIAL PRIMARY KEY,
    user_id         INTEGER REFERENCES users (id) ON DELETE CASCADE,
    workspace_id    INTEGER REFERENCES workspace (id) ON DELETE CASCADE,
    type            VARCHAR(32),
    components      JSONB NOT NULL DEFAULT '{}'::jsonb,
    deleted_by      BIGINT,
    deleted_date    TIMESTAMP DEFAULT now()
);

-- -----------------------------------------------------
-- Table `query_validation`
-- This table should be left blank
-- -----------------------------------------------------

CREATE TABLE IF NOT EXISTS query_validation
(
    id      SERIAL PRIMARY KEY,
    content TEXT,
    ts_vector_data    tsvector GENERATED ALWAYS AS (to_tsvector('english', replace(content, '''', ''))) STORED
);

CREATE TABLE IF NOT EXISTS survey
(
    id                       SERIAL PRIMARY KEY,
    survey_uuid              VARCHAR(128),
    user_id                  INTEGER REFERENCES users (id) ON DELETE CASCADE,
    dataset_id               INTEGER REFERENCES bulk_upload (id) ON DELETE CASCADE,
    workspace_id             INTEGER REFERENCES workspace (id) ON DELETE CASCADE,
    namespace                VARCHAR(128),
    label                    VARCHAR(128),
    description              TEXT,
    status                   VARCHAR(128),
    questions                JSONB,
    configurations           JSONB,
    total_responses          INTEGER,
    total_analysed_responses INTEGER,
    created_at               TIMESTAMP,
    updated_at               TIMESTAMP
);

CREATE TABLE IF NOT EXISTS storyteller_report (
    id SERIAL PRIMARY KEY,
    dataset_id INTEGER REFERENCES bulk_upload(id),
    report_type VARCHAR(255),
    theme_ids INTEGER[] DEFAULT '{}',
    action_plan_theme_ids INTEGER[] DEFAULT '{}',
    settings JSONB DEFAULT '{}'::jsonb,
    user_id INTEGER REFERENCES users (id),
    created_at TIMESTAMP DEFAULT NOW()
);

CREATE TABLE IF NOT EXISTS storyteller_slide (
   id SERIAL PRIMARY KEY,
   report_id INTEGER REFERENCES storyteller_report(id),
   slide_order INTEGER,
   slide_type VARCHAR(255),
   slide_data JSONB DEFAULT '{}'::jsonb,
   created_at TIMESTAMP DEFAULT NOW()
);

CREATE TABLE IF NOT EXISTS theme_summary
(
    id         SERIAL PRIMARY KEY,
    dataset_id INTEGER NOT NULL REFERENCES bulk_upload (id) ,
    theme_id   INTEGER NULL REFERENCES user_topic (id) ,
    summary    TEXT    NULL
);

-- -----------------------------------------------------
-- Dataset Summary
-- -----------------------------------------------------

CREATE TABLE IF NOT EXISTS dataset_summary
(
    id              SERIAL PRIMARY KEY,
    dataset_id      INTEGER NOT NULL REFERENCES bulk_upload (id) ON DELETE CASCADE,
    summary         JSONB DEFAULT '{}'::JSONB
);

-- -----------------------------------------------------
-- TABLE `rating_scale`
-- -----------------------------------------------------
CREATE TABLE rating_scale (
    id SERIAL           PRIMARY KEY,
    user_id             INTEGER NOT NULL,
    workspace_id        INTEGER NOT NULL,
    name                VARCHAR(128) NOT NULL,
    rating_scale_map    JSONB NOT NULL,
    created_at          TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
CREATE INDEX idx_rating_scale_user_id ON rating_scale(user_id);
CREATE INDEX idx_rating_scale_workspace_id ON rating_scale(workspace_id);

-- -----------------------------------------------------
-- VIEW `JOURNEY_STAGE_SEARCH_VIEW`
-- -----------------------------------------------------

CREATE OR REPLACE VIEW journey_stage_search_view AS
(
SELECT js.*,
       s.query,
       s.exact,
       s.distinct_content,
       s.score_filters,
       s.exclude_content_ids
FROM journey_stage js
         LEFT OUTER JOIN search_components s ON js.search_id = s.id
    );


-- -----------------------------------------------------
-- Tables for REPORTING JOB
-- -----------------------------------------------------

CREATE TABLE reporting_job (
    id                          SERIAL PRIMARY KEY,
    user_id                     INT REFERENCES users (id) NOT NULL,
    workspace_id                INT REFERENCES workspace (id) NOT NULL,
    name                        VARCHAR(255) NOT NULL,
    schedule_type               VARCHAR(50), -- DAILY, MONTHLY, QUARTERLY, YEARLY
    schedule_time               TIME,
    schedule_day                INT CHECK (schedule_day BETWEEN 1 AND 31),
    schedule_month_in_quarter   INT CHECK (schedule_month_in_quarter BETWEEN 1 AND 3),  -- 1 = First month, 2 = Second month, 3 = Third month of the quarter
    schedule_month_in_year      INT CHECK (schedule_month_in_year BETWEEN 1 AND 12),
    execution_number            INT NOT NULL,
    created_at                  TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP
);
CREATE INDEX idx_user_id_on_reporting_job ON reporting_job(user_id);

CREATE TABLE reporting_job_data_source (
    id                              SERIAL PRIMARY KEY,
    reporting_job_id                INTEGER REFERENCES reporting_job ON DELETE CASCADE,
    data_type                       VARCHAR(50),
    data_id                         TEXT NOT NULL,
    data_collection_type            VARCHAR(50) NOT NULL DEFAULT 'ENTIRE_LIFE',
    -- schedule_type is DAILY = [ENTIRE_LIFE, CUSTOM, TODAY, YESTERDAY, LAST_24_HOURS]
    -- schedule_type is MONTHLY = [ENTIRE_LIFE, CUSTOM, THIS_MONTH, LAST_MONTH, LAST_30_DAYS]
    -- schedule_type is QUARTERLY = [ENTIRE_LIFE, CUSTOM, THIS_QUARTER, LAST_QUARTER, LAST_90_DAYS]
    -- schedule_type is YEARLY = [ENTIRE_LIFE, CUSTOM, THIS_YEAR, LAST_YEAR, LAST_365_DAYS]
    data_custom_start_date          TIMESTAMP,
    data_custom_end_date            TIMESTAMP,
    pre_processing_acknowledgement  JSONB DEFAULT '{}'::JSONB
);
CREATE INDEX idx_reporting_job_id_on_data_source ON reporting_job_data_source(reporting_job_id);

CREATE TABLE reporting_job_split_rule (
    id SERIAL           PRIMARY KEY,
    reporting_job_id    INT REFERENCES reporting_job(id) ON DELETE CASCADE,
    metadata_key        VARCHAR(255) NOT NULL,
    split_settings      JSONB DEFAULT '{}'::JSONB
);
CREATE INDEX idx_reporting_job_id_on_split_rule ON reporting_job_split_rule(reporting_job_id);

CREATE TABLE reporting_job_workspace_rule (
    id               SERIAL PRIMARY KEY,
    reporting_job_id INT REFERENCES reporting_job(id) ON DELETE CASCADE,
    workspace_settings JSONB DEFAULT'{}'::JSONB
);
CREATE INDEX idx_reporting_job_id_on_workspace_rule ON reporting_job_workspace_rule(reporting_job_id);

CREATE TABLE reporting_job_search_theme (
    id SERIAL            PRIMARY KEY,
    reporting_job_id     INT REFERENCES reporting_job(id) ON DELETE CASCADE,
    search_theme_settings JSONB DEFAULT '{}'::JSONB
);
CREATE INDEX idx_reporting_job_id_on_search_theme ON reporting_job_search_theme(reporting_job_id);

CREATE TABLE reporting_job_email_config (
    id                    SERIAL PRIMARY KEY,
    reporting_job_id      INT REFERENCES reporting_job(id) ON DELETE CASCADE,
    sender_email          VARCHAR(255) NULL,
    auto_send             BOOLEAN DEFAULT FALSE,
    template_id           VARCHAR(255) DEFAULT 'reporting-email-template',
    subject               VARCHAR(255) NULL,
    email_settings        JSONB DEFAULT '{}'::JSONB
);

CREATE INDEX idx_reporting_job_id_on_email_config ON reporting_job_email_config(reporting_job_id);

CREATE TABLE reporting_job_result (
    id                      SERIAL PRIMARY KEY,
    reporting_job_id        INT REFERENCES reporting_job(id) ON DELETE CASCADE,
    execution_number        INT,
    dataset_id              INT,
    dataset_original_name   VARCHAR(255) NOT NULL,
    volume                  INT,
    volume_change           INT,
    adorescore              INT,
    adorescore_change       INT,
    start_date              TIMESTAMP,
    end_date                TIMESTAMP,
    metric_results          JSONB,
    topic_results           JSONB,
    filtered_topic_names    TEXT[] DEFAULT '{}',
    created_at              TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
CREATE INDEX idx_reporting_job_result            ON reporting_job_result(reporting_job_id);
CREATE INDEX idx_reporting_job_result_dataset_id ON reporting_job_result(dataset_id);

CREATE TABLE reporting_job_execution_status (
    id                  SERIAL PRIMARY KEY,
    reporting_job_id    INT REFERENCES reporting_job(id) ON DELETE CASCADE,
    execution_number    INT NOT NULL,
    created_at          TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    finished_at         TIMESTAMP,
    step                VARCHAR(50), -- SOURCING, ANALYSING, MERGING, SPLITTING, RENAMING, SEARCHING_THEMES, COMPARING_DATA, EMAILING
    status              VARCHAR(50), -- RUNNING, FAILED, SUCCESS
    error_message       TEXT
);
CREATE INDEX idx_reporting_job_id_on_execution_status ON reporting_job_execution_status(reporting_job_id);

-- -----------------------------------------------------
-- VIEW `BULK_UPLOAD_VIEW`
-- -----------------------------------------------------

CREATE OR REPLACE VIEW bulk_upload_view AS
(
WITH bupsm AS (SELECT bulk_upload_id AS dataset_id,
                      JSONB_OBJECT_AGG(
                              status,
                              JSONB_BUILD_OBJECT(
                                      'status', status,
                                      'startedAt', started_at,
                                      'updatedAt', updated_at,
                                      'numOfItems', num_of_items,
                                      'currentItem', current_item
                                  )
                          )          AS progress_status_map
               FROM bulk_upload_status
               GROUP BY dataset_id),
     busw AS (SELECT bulk_upload_id AS dataset_id,
                     JSONB_AGG(
                             JSONB_BUILD_OBJECT(
                                     'id', id,
                                     'stopWord', stop_word,
                                     'status', status,
                                     'created', created
                                 )
                         )
              FROM stop_word
              GROUP BY dataset_id),
     ddd AS (SELECT *
             FROM dataset_download_details)
SELECT bu.*,
       (bu.summary ->> 'polarity') :: DOUBLE PRECISION AS polarity,
       (bu.summary ->> 'adoreScore') :: NUMERIC        AS adorescore,
       bupsm.progress_status_map                       AS progress_status_map,
       busw.jsonb_agg                                  AS stop_words,
       ddd.analysis_download_location                  AS analysis_download_location,
       ddd.original_download_location                  AS original_download_location
FROM bulk_upload bu
         LEFT OUTER JOIN bupsm ON bupsm.dataset_id = bu.id
         LEFT OUTER JOIN busw ON busw.dataset_id = bu.id
         LEFT OUTER JOIN ddd ON ddd.dataset_id = bu.id
    );

-- -----------------------------------------------------
-- VIEW `DATASET_PERMISSION_VIEW`
-- -----------------------------------------------------

CREATE OR REPLACE VIEW dataset_permission_view AS
(
SELECT dp.*,
       (CASE
            WHEN dp.permission_type = NULL THEN 1
            WHEN dp.permission_type = 'RESTRICTED' THEN 2
            WHEN dp.permission_type = 'GROUP' THEN 3
            WHEN dp.permission_type = 'PUBLIC' THEN 4
            ELSE 5
           END)                                                                                                                  AS permission_order,
       COALESCE((SELECT COUNT(v) FROM UNNEST(dp.viewer_ids) v), 0) +
       COALESCE((SELECT COUNT(e) FROM UNNEST(dp.editor_ids) e), 0)                                                               AS total_users,
       wg.label                                                                                                                  AS group_label
FROM dataset_permission dp
         LEFT JOIN workspace_group wg ON dp.group_id = wg.id
    );
-- ----------------------------------------------------------------------------------------------------------
-- Utility view to easily show indexes across tables / materialized views
-- ----------------------------------------------------------------------------------------------------------

CREATE OR REPLACE VIEW _view_index AS
SELECT n.nspname                   AS schema,
       t.relname                   AS table,
       c.relname                   AS index,
       PG_GET_INDEXDEF(indexrelid) AS def
FROM pg_catalog.pg_class c
         JOIN pg_catalog.pg_namespace n ON n.oid = c.relnamespace
         JOIN pg_catalog.pg_index i ON i.indexrelid = c.oid
         JOIN pg_catalog.pg_class t ON i.indrelid = t.oid
WHERE c.relkind = 'i'
  AND n.nspname NOT IN ('pg_catalog', 'pg_toast')
  AND pg_catalog.PG_TABLE_IS_VISIBLE(c.oid)
ORDER BY n.nspname
       , t.relname
       , c.relname;

CREATE OR REPLACE VIEW topic_doc_var_view AS
SELECT
    ud.bulk_upload_id,
    utd.user_topic_id,
    AVG(ud.reweighted_score) AS mean_reweighted_score,
    COUNT(ud.id) AS count_per_theme
FROM
    user_doc ud
        JOIN
    user_topic_doc utd ON utd.user_doc_id = ud.id
GROUP BY
    utd.user_topic_id, ud.bulk_upload_id;

-- Set this will force postgresql to likely use the Index Join (instead of HashJoin)
-- https://www.cybertec-postgresql.com/en/better-postgresql-performance-on-ssds/#

SET RANDOM_PAGE_COST = 1;
