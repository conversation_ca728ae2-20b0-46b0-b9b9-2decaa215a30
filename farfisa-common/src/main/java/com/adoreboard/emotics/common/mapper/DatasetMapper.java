/*
 * Copyright 2022 Adoreboard Ltd. All rights reserved.
 * ADOREBOARD PROPRIETARY/CONFIDENTIAL. Use is subject to license terms.
 */

package com.adoreboard.emotics.common.mapper;

import com.adoreboard.emotics.common.enums.DatasetStatus;
import com.adoreboard.emotics.common.enums.TopicType;
import com.adoreboard.emotics.common.mapper.param.DatasetParam;
import com.adoreboard.emotics.common.model.*;
import com.adoreboard.emotics.common.model.analysis.AnalysisSummary;
import com.adoreboard.emotics.common.model.metadata.MetadataFilterListType;
import com.adoreboard.emotics.common.model.query.DatasetLite;
import com.adoreboard.emotics.common.model.query.DatasetQuery;
import com.adoreboard.emotics.common.model.revenueAtRisk.ValueAtRiskInfo;
import com.adoreboard.emotics.common.model.revenueAtRisk.ValueAtRiskInfoObject;
import org.apache.ibatis.annotations.MapKey;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * Mapper containing methods relating to datasets.
 * <AUTHOR> <PERSON>
 */
public interface DatasetMapper {

    /**
     * Insert dataset object.
     *
     * @param dataset
     */
    void insert(Dataset dataset);

    /**
     * Update an existing dataset object.
     *
     * @param dataset
     */
    void update(Dataset dataset);

    /**
     * Update status of a dataset
     *
     * @param datasetId
     * @param status
     */
    void updateStatus(@Param("datasetId") long datasetId, @Param("status") DatasetStatus status, @Param("ignoreList") List<DatasetStatus> ignoreList);

    /**
     * Update error of a dataset
     *
     * @param datasetError
     */
    void updateError(@Param("datasetId") long datasetId,  @Param("datasetError") DatasetError datasetError);

    /**
     * Update summary of a job
     *
     * @param datasetId
     * @param summary
     */
    void updateSummary(@Param("datasetId") long datasetId, @Param("summary") AnalysisSummary summary);

    /**
     * Set the pending_changes column to true
     *
     * @param datasetId
     */
    void updatePendingChanges(@Param("datasetId") long datasetId, @Param("hasPendingChanges") boolean hasPendingChanges);

    /**
     * Set the name
     *
     * @param datasetId
     * @param label
     */
    void rename(@Param("datasetId") int datasetId, @Param("label") String label);

    /**
     * Update document count based on what's in user_doc or user_content table
     *
     * @param datasetId
     * @param isSummaryCount: true = use 'user_doc' table, false = use 'user_content' table
     */
    void updateDocCount(@Param("datasetId") int datasetId, @Param("isSummaryCount") boolean isSummaryCount);

    /**
     * Update char count of a dataset
     *
     * @param datasetId
     * @param charCount
     */
    void updateCharCount(@Param("datasetId") int datasetId, @Param("charCount") long charCount);

    /**
     * Delete single dataset.
     *
     * @param datasetId
     */
    void delete(int datasetId);

    /**
     * Given a user id, select only info specific to the job list
     *
     * @param userId
     * @return
     */
    List<Dataset> selectByUserId(int userId);

    /**
     * @param
     * @param datasetIds
     * @return
     */
    List<Dataset> selectByUserIdAndIds(@Param("userId") int userId, @Param("datasetIds") List<Integer> datasetIds);

    /**
     * @param datasetId
     * @return
     */
    ValueAtRiskInfoObject selectValueAtRiskInfo(@Param("datasetId") int datasetId);

    /**
     * @param datasetId
     * @return
     */
    BigDecimal selectValueAtRiskAmount(
              @Param("datasetId") int datasetId
            , @Param("costPerComment") double costPerComment
            , @Param("scaleToTotalPeople") boolean scaleToTotalPeople
            , @Param("numberOfScalePeople") int numberOfScalePeople
            , @Param("totalYear") int totalYear);

    /**
     * Count datasets
     *
     * @param userId
     * @param workspaceIds
     * @param datasetIds
     * @return number of datasets that belongs to userId
     */
    int countDatasets(@Param("userId") int userId, @Param("workspaceIds") List<Integer> workspaceIds, @Param("datasetIds") List<Integer> datasetIds);

    /**
     * Select dataset by Id
     *
     * @param datasetId
     * @return
     */
    Dataset selectById(int datasetId);

    /**
     * @param datasetIds
     * @return
     */
    List<Dataset> selectByIds(List<Integer> datasetIds);

    /**
     *
     * @param datasetIds
     * @param topicType
     * @return
     */
    List<DatasetLite> selectLite(@Param("datasetIds")List<Integer> datasetIds, @Param("topicType") TopicType topicType);

    /**
     * Return document count of a dataset
     *
     * @param datasetId
     * @return
     */
    int selectDocumentCountByDatasetId(int datasetId);

    /**
     * Query datasets.
     *
     * @param datasetParam
     * @return
     */
    List<DatasetQuery> query(DatasetParam datasetParam);

    /**
     * Delete datasets associated with user.
     *
     * @param userId
     */
    void deleteForUserId(int userId);

    /**
     * Delete one or more items by id.
     *
     * @param datasetIds
     */
    void deleteIds(List<Integer> datasetIds);

    /**
     * Return a map containing counts by status.
     *
     * @return
     */
    @MapKey("key") Map<String, KeyValue<String, Long>> selectStatusCounts();

    /**
     * Increment the execution id for a particular dataset id.
     *
     * @param datasetId
     */
    void incrementExecutionId(int datasetId);

    /**
     *
     * @param status
     * @param datasetIds
     * @return
     */
    Integer countStatusForIds(@Param("status") String status, @Param("datasetIds") List<Integer> datasetIds);

    /**
     *
     * @param datasetIds
     * @param archived
     */
    void updateArchived(@Param("datasetIds") List<Integer> datasetIds, @Param("isArchived") boolean archived);

    /**
     * Update a metadata.
     * @param dataset
     */
    void updateMetadata(Dataset dataset);

    /**
     * Update a metadata header name.
     *
     * @param datasetId
     * @param metadataHeaderIndex
     * @param metadataHeaderLabel
     */
    void updateMetadataHeader(@Param("datasetId") Integer datasetId, @Param("metadataHeaderIndex") Integer metadataHeaderIndex, @Param("metadataHeaderLabel") String metadataHeaderLabel);

    /**
     * Update the VAR info for a dataset.
     *
     * @param datasetId
     * @param valueAtRiskInfo
     */
    void updateVarInfo(@Param("datasetId") Integer datasetId, @Param("valueAtRiskInfo") ValueAtRiskInfo valueAtRiskInfo);

    /**
     * Update the Rating Scale Maps.
     *
     * @param datasetId
     * @param ratingScaleMaps
     */
    void updateRatingScaleMaps(@Param("datasetId") Integer datasetId, @Param("ratingScaleMaps") Map<Integer, Map<String, Integer>> ratingScaleMaps);

    /**
     * @param datasetIds
     * @return list of saved action ids
     */
    List<Integer> selectDistinctSavedActionIdsByDatasetIds(@Param("datasetIds") List<Integer> datasetIds);

    /**
     *
     * @param datasetIds
     * @return unique workspace ids
     */
    List<Integer> selectWorkspaceIdsByDatasetIds(@Param("datasetIds") List<Integer> datasetIds);

    /**
     *
     * @param datasetId
     * @return workspace id
     */
    Integer selectWorkspaceIdByDatasetId(@Param("datasetId") Integer datasetId);

    /**
     * @param userId
     * @param datasetId
     * @return true if dataset belongs to user
     */
    Boolean isOwner(@Param("userId") Integer userId, @Param("datasetId") Integer datasetId);

    /**
     * @param datasetId
     * @return userId
     */
    Integer selectUserIdById(@Param("datasetId") Integer datasetId);

    /**
     * @param userId
     * @param parentId
     * @return list of datasetIds
     */
    List<Integer> selectChildrenIdsByStatusAndParams(@Param("userId") Integer userId, @Param("parentId") Integer parentId, @Param("status") DatasetStatus status, @Param("filterTypes") List<MetadataFilterListType> filterTypes, @Param("filterIds") List<Integer> filterIds);

}
