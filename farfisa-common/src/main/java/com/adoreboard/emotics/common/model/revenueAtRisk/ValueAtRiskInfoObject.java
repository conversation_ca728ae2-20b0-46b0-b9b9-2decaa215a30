package com.adoreboard.emotics.common.model.revenueAtRisk;

import com.adoreboard.emotics.common.model.ValueAtRiskInfo;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class ValueAtRiskInfoObject {
    private Integer templateId;
    private ValueAtRiskInfo valueAtRiskInfo;
}
