package com.adoreboard.emotics.common.mapper;

import com.adoreboard.emotics.common.model.revenueAtRisk.DatasetRevenueAtRisk;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface DatasetRevenueAtRiskMapper {

    // Dataset template operations
    List<DatasetRevenueAtRisk> getDatasetTemplates(@Param("datasetId") Integer datasetId);

    DatasetRevenueAtRisk getDatasetTemplateById(@Param("id") Integer id);

    DatasetRevenueAtRisk getDatasetTemplateByTemplateId(@Param("datasetId") Integer datasetId, @Param("templateId") Integer templateId);

    Integer createDatasetTemplate(DatasetRevenueAtRisk datasetTemplate);

    void updateDatasetTemplate(DatasetRevenueAtRisk datasetTemplate);

    void deleteDatasetTemplate(@Param("id") Integer id);

    void deleteDatasetTemplatesByDatasetId(@Param("datasetId") Integer datasetId);

    // Bulk operations for dataset creation
    void cloneWorkspaceTemplatesForDataset(@Param("datasetId") Integer datasetId, @Param("workspaceId") Integer workspaceId);

    // Count operations
    int countDatasetTemplates(@Param("datasetId") Integer datasetId);
}
